using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.SQLite;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using NAVI.Controls;
using NAVI.Models;
using NAVI.Services;
using NAVI.Services.DAL;
using NAVI.Utils;
using NAVI.Windows;

namespace NAVI
{
    /// <summary>
    /// 受给者导入数据
    /// </summary>
    public partial class RecipientServiceInfoControl : UserControl
    {
        private ObservableCollection<RecipientServiceInfo> _recipientServiceInfoList;
        private List<RecipientServiceInfo> _allData;
        private List<string> _columnNames;
        private DatabaseManager _databaseManager;
        private RecipientRepository _recipientRepository;

        // 分页相关
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalRecords = 0;

        public RecipientServiceInfoControl()
        {
            InitializeComponent();
            InitializeData();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化数据库管理器
                _databaseManager = new DatabaseManager();
                _recipientRepository = _databaseManager.Recipients;

                // 从数据库加载数据
                LoadDataFromDatabase();

                // 设置搜索框的占位符效果
                SetupSearchBoxPlaceholder();

                // 初始化分页
                UpdatePagination();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ初期化に失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                LoadSampleData();
            }
        }

        /// <summary>
        /// 从数据库加载数据
        /// </summary>
        private void LoadDataFromDatabase()
        {
            try
            {
                // 设置列名（隐藏No列作为主键）
                _columnNames = new List<string>
                {
                    "登録日", "事業者番号", "事業者郵便番号", "事業者住所", "事業者名称", "代表者名", "代表者役職",
                    "サービス提供年月", "明細書件数", "請求金額", "第三者評価", "受給者番号", "支給決定障害者氏名", "支給決定に係る障害児氏名",
                    "障害支援区分", "事業者名称2", "地域区分", "旧身体療護施設区分", "精神科医療連携体制加算", "開始年月日", "終了年月日",
                    "利用日数全体", "サービスコード", "サービス内容", "算定単価額", "利用日数", "当月算定額", "摘要", "status"
                };

                // 创建动态列
                CreateDynamicColumns();

                // 使用分页查询获取数据
                Task.Run(async () => await LoadPagedDataAsync());
            }
            catch (Exception ex)
            {
                throw new Exception($"从数据库加载数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 加载分页数据
        /// </summary>
        private async Task LoadPagedDataAsync()
        {
            try
            {
                string searchText = null;

                Dispatcher.Invoke(() =>
                {
                    searchText = SearchTextBox?.Text?.Trim();
                });
                string whereClause = "";
                var parameters = new List<SQLiteParameter>();

                // 构建搜索条件
                if (!string.IsNullOrEmpty(searchText) && searchText != "受給者番号・サービス名・事業者等のキーワードを入力")
                {
                    whereClause = @"""受給者番号"" LIKE @search OR ""事業者番号"" LIKE @search OR ""事業者名称"" LIKE @search OR
                                   ""支給決定障害者氏名"" LIKE @search OR ""サービス内容"" LIKE @search";
                    parameters.Add(new SQLiteParameter("@search", $"%{searchText}%"));
                }

                // 使用分页查询
                var (recipientData, totalCount) = await _recipientRepository.GetPagedAsync(
                    _currentPage, _pageSize, whereClause, parameters.ToArray());

                _totalRecords = totalCount;

                // 转换数据
                var recipientServiceInfoList = ConvertRecipientDataToRecipientServiceInfo(recipientData);
                _recipientServiceInfoList = new ObservableCollection<RecipientServiceInfo>(recipientServiceInfoList);

                // 更新UI（需要在UI线程中执行）
                Application.Current.Dispatcher.Invoke(() =>
                {
                    RecipientServiceInfoGrid.ItemsSource = _recipientServiceInfoList;
                    UpdatePagination();
                    UpdateStatusInfo();
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"加载分页数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 加载模拟数据
        /// </summary>
        private void LoadSampleData()
        {
            _allData = RecipientServiceInfoService.GetSampleData();
            if (_allData.Any())
            {
                _columnNames = _allData.First().PropertyNames.ToList();
                CreateDynamicColumns();
                ApplyPagination();
            }
        }

        /// <summary>
        /// 将RecipientData转换为RecipientServiceInfo
        /// </summary>
        private List<RecipientServiceInfo> ConvertRecipientDataToRecipientServiceInfo(List<RecipientData> recipientDataList)
        {
            var recipientServiceInfoList = new List<RecipientServiceInfo>();

            foreach (var data in recipientDataList)
            {
                var recipientServiceInfo = new RecipientServiceInfo();
                recipientServiceInfo.SetProperty("No", data.No);
                recipientServiceInfo.SetProperty("登録日", data.登録日);
                recipientServiceInfo.SetProperty("事業者番号", data.事業者番号);
                recipientServiceInfo.SetProperty("事業者郵便番号", data.事業者郵便番号);
                recipientServiceInfo.SetProperty("事業者住所", data.事業者住所);
                recipientServiceInfo.SetProperty("事業者名称", data.事業者名称);
                recipientServiceInfo.SetProperty("代表者名", data.代表者名);
                recipientServiceInfo.SetProperty("代表者役職", data.代表者役職);
                recipientServiceInfo.SetProperty("サービス提供年月", data.サービス提供年月);
                recipientServiceInfo.SetProperty("明細書件数", data.明細書件数);
                recipientServiceInfo.SetProperty("請求金額", data.請求金額);
                recipientServiceInfo.SetProperty("第三者評価", data.第三者評価);
                recipientServiceInfo.SetProperty("受給者番号", data.受給者番号);
                recipientServiceInfo.SetProperty("支給決定障害者氏名", data.支給決定障害者氏名);
                recipientServiceInfo.SetProperty("支給決定に係る障害児氏名", data.支給決定に係る障害児氏名);
                recipientServiceInfo.SetProperty("障害支援区分", data.障害支援区分);
                recipientServiceInfo.SetProperty("事業者名称2", data.事業者名称2);
                recipientServiceInfo.SetProperty("地域区分", data.地域区分);
                recipientServiceInfo.SetProperty("旧身体療護施設区分", data.旧身体療護施設区分);
                recipientServiceInfo.SetProperty("精神科医療連携体制加算", data.精神科医療連携体制加算);
                recipientServiceInfo.SetProperty("開始年月日", data.開始年月日);
                recipientServiceInfo.SetProperty("終了年月日", data.終了年月日);
                recipientServiceInfo.SetProperty("利用日数全体", data.利用日数全体);
                recipientServiceInfo.SetProperty("サービスコード", data.サービスコード);
                recipientServiceInfo.SetProperty("サービス内容", data.サービス内容);
                recipientServiceInfo.SetProperty("算定単価額", data.算定単価額);
                recipientServiceInfo.SetProperty("利用日数", data.利用日数);
                recipientServiceInfo.SetProperty("当月算定額", data.当月算定額);
                recipientServiceInfo.SetProperty("摘要", data.摘要);
                recipientServiceInfo.SetProperty("status", data.status);

                recipientServiceInfoList.Add(recipientServiceInfo);
            }

            return recipientServiceInfoList;
        }

        /// <summary>
        /// 创建动态列
        /// </summary>
        private void CreateDynamicColumns()
        {
            // 清除除操作列外的所有列
            var operationColumn = RecipientServiceInfoGrid.Columns.FirstOrDefault();
            RecipientServiceInfoGrid.Columns.Clear();
            if (operationColumn != null)
            {
                RecipientServiceInfoGrid.Columns.Add(operationColumn);
            }
            //RecipientServiceInfoGrid.Columns.Clear();

            if (_columnNames == null || !_columnNames.Any()) return;
            var displayStyle = (Style)FindResource("CellLeftAlignDisplayStyle");
            var editStyle = (Style)FindResource("CellLeftAlignEditStyle");
            foreach (var columnName in _columnNames)
            {
                var column = new DataGridTextColumn
                {
                    Header = columnName,
                    Binding = new System.Windows.Data.Binding(columnName),
                    Width = GetColumnWidth(columnName),
                    // 👇 添加样式
                    ElementStyle = displayStyle,            // 给 TextBlock 用
                    EditingElementStyle = editStyle         // 给 TextBox 用
                };

                RecipientServiceInfoGrid.Columns.Add(column);
            }
        }

        /// <summary>
        /// 根据列名获取合适的列宽
        /// </summary>
        private DataGridLength GetColumnWidth(string columnName)
        {
            return new DataGridLength(120);
            /*return columnName switch
            {
                "受给者ID" => new DataGridLength(80),
                "受给者姓名" => new DataGridLength(120),
                "生年月日" => new DataGridLength(100),
                "性别" => new DataGridLength(60),
                "住所" => new DataGridLength(200),
                "电话番号" => new DataGridLength(120),
                "服务代码" => new DataGridLength(80),
                "服务名称" => new DataGridLength(150),
                "利用开始日" => new DataGridLength(100),
                "利用结束日" => new DataGridLength(100),
                "月利用回数" => new DataGridLength(80),
                "单价" => new DataGridLength(80),
                "月额费用" => new DataGridLength(100),
                "负担额" => new DataGridLength(80),
                "状态" => new DataGridLength(80),
                "备注" => new DataGridLength(150),
                _ => new DataGridLength(100)
            };*/
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 分页控件事件
            PaginationControl.PageChanged += PaginationControl_PageChanged;
            PaginationControl.PageSizeChanged += PaginationControl_PageSizeChanged;

            // DataGrid事件
            RecipientServiceInfoGrid.SelectionChanged += RecipientServiceInfoGrid_SelectionChanged;

            // 搜索框事件
            SearchTextBox.GotFocus += SearchTextBox_GotFocus;
            SearchTextBox.LostFocus += SearchTextBox_LostFocus;
            SearchTextBox.TextChanged += SearchTextBox_TextChanged;
        }

        /// <summary>
        /// 设置搜索框占位符效果
        /// </summary>
        private void SetupSearchBoxPlaceholder()
        {
            SearchTextBox.Foreground = Brushes.Gray;
        }

        /// <summary>
        /// 搜索框获得焦点事件
        /// </summary>
        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "受給者番号・サービス名・事業者等のキーワードを入力")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = Brushes.Black;
            }
        }

        /// <summary>
        /// 搜索框失去焦点事件
        /// </summary>
        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "受給者番号・サービス名・事業者等のキーワードを入力";
                SearchTextBox.Foreground = Brushes.Gray;
            }
        }

        /// <summary>
        /// 搜索框文本变化事件
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _currentPage = 1; // 重置到第一页
            ApplyPagination();
        }

        /// <summary>
        /// DataGrid选择变化事件
        /// </summary>
        private void RecipientServiceInfoGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateStatusInfo();
        }

        /// <summary>
        /// 应用分页
        /// </summary>
        /*private void ApplyPagination()
        {
            var filteredData = GetFilteredData();
            _totalRecords = filteredData.Count;

            var pagedData = filteredData
                .Skip((_currentPage - 1) * _pageSize)
                .Take(_pageSize)
                .ToList();

            _recipientServiceInfoList = new ObservableCollection<RecipientServiceInfo>(pagedData);
            RecipientServiceInfoGrid.ItemsSource = _recipientServiceInfoList;

            UpdatePagination();
            UpdateStatusInfo();
        }*/

        /// <summary>
        /// 更新分页信息
        /// </summary>
        private void UpdatePagination()
        {
            var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);

            PaginationControl.CurrentPage = _currentPage;
            PaginationControl.TotalPages = Math.Max(1, totalPages);
            PaginationControl.TotalRecords = _totalRecords;
            PaginationControl.PageSize = _pageSize;
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = 1;
            ApplyPagination();
        }

        /// <summary>
        /// 应用分页
        /// </summary>
        private void ApplyPagination()
        {
            // 使用数据库分页查询
            Task.Run(async () => await LoadPagedDataAsync());
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatusInfo()
        {
            int selectedCount = RecipientServiceInfoGrid.SelectedItems.Count;

            // 尝试更新主窗口的状态栏
            var mainWindow = Application.Current.MainWindow as MainWindow;
            mainWindow?.UpdateStatusBar(_totalRecords, selectedCount, PaginationControl.TotalPages, "");
        }

        /// <summary>
        /// 分页控件页码变化事件
        /// </summary>
        private void PaginationControl_PageChanged(object sender, PageChangedEventArgs e)
        {
            _currentPage = e.NewPage;
            ApplyPagination();
        }

        /// <summary>
        /// 分页控件页面大小变化事件
        /// </summary>
        private void PaginationControl_PageSizeChanged(object sender, PageSizeChangedEventArgs e)
        {
            _pageSize = e.NewPageSize;
            _currentPage = 1; // 重置到第一页
            ApplyPagination();
        }


        /// <summary>
        /// 新增按钮点击事件
        /// </summary>
        private async void AddButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editWindow = new EditWindow("新規受給者データ追加", _columnNames);
                if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                {
                    // 创建RecipientData对象
                    var recipientData = new RecipientData
                    {
                        登録日 = editWindow.ResultData.GetValueOrDefault("登録日", "").ToString(),
                        事業者番号 = editWindow.ResultData.GetValueOrDefault("事業者番号", "").ToString(),
                        事業者郵便番号 = editWindow.ResultData.GetValueOrDefault("事業者郵便番号", "").ToString(),
                        事業者住所 = editWindow.ResultData.GetValueOrDefault("事業者住所", "").ToString(),
                        事業者名称 = editWindow.ResultData.GetValueOrDefault("事業者名称", "").ToString(),
                        代表者名 = editWindow.ResultData.GetValueOrDefault("代表者名", "").ToString(),
                        代表者役職 = editWindow.ResultData.GetValueOrDefault("代表者役職", "").ToString(),
                        サービス提供年月 = editWindow.ResultData.GetValueOrDefault("サービス提供年月", "").ToString(),
                        明細書件数 = editWindow.ResultData.GetValueOrDefault("明細書件数", "0").ToString(),
                        請求金額 = editWindow.ResultData.GetValueOrDefault("請求金額", "0").ToString(),
                        第三者評価 = editWindow.ResultData.GetValueOrDefault("第三者評価", "").ToString(),
                        受給者番号 = editWindow.ResultData.GetValueOrDefault("受給者番号", "").ToString(),
                        支給決定障害者氏名 = editWindow.ResultData.GetValueOrDefault("支給決定障害者氏名", "").ToString(),
                        支給決定に係る障害児氏名 = editWindow.ResultData.GetValueOrDefault("支給決定に係る障害児氏名", "").ToString(),
                        障害支援区分 = editWindow.ResultData.GetValueOrDefault("障害支援区分", "").ToString(),
                        事業者名称2 = editWindow.ResultData.GetValueOrDefault("事業者名称2", "").ToString(),
                        地域区分 = editWindow.ResultData.GetValueOrDefault("地域区分", "").ToString(),
                        旧身体療護施設区分 = editWindow.ResultData.GetValueOrDefault("旧身体療護施設区分", "").ToString(),
                        精神科医療連携体制加算 = editWindow.ResultData.GetValueOrDefault("精神科医療連携体制加算", "").ToString(),
                        開始年月日 = editWindow.ResultData.GetValueOrDefault("開始年月日", "").ToString(),
                        終了年月日 = editWindow.ResultData.GetValueOrDefault("終了年月日", "").ToString(),
                        利用日数全体 = editWindow.ResultData.GetValueOrDefault("利用日数全体", "0").ToString(),
                        サービスコード = editWindow.ResultData.GetValueOrDefault("サービスコード", "").ToString(),
                        サービス内容 = editWindow.ResultData.GetValueOrDefault("サービス内容", "").ToString(),
                        算定単価額 = editWindow.ResultData.GetValueOrDefault("算定単価額", "0").ToString(),
                        利用日数 = editWindow.ResultData.GetValueOrDefault("利用日数", "0").ToString(),
                        当月算定額 = editWindow.ResultData.GetValueOrDefault("当月算定額", "0").ToString(),
                        摘要 = editWindow.ResultData.GetValueOrDefault("摘要", "").ToString(),
                        status = editWindow.ResultData.GetValueOrDefault("status", "").ToString()
                    };

                    // 保存到数据库
                    await _recipientRepository.CreateRecipientAsync(recipientData);

                    // 刷新数据
                    LoadDataFromDatabase();
                    MessageBox.Show("データの追加が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ追加に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 编辑按钮点击事件
        /// </summary>
        private async void EditButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var selectedItem = button?.Tag as RecipientServiceInfo;
            if (selectedItem == null)
            {
                MessageBox.Show("編集するレコードを選択してください！", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var editData = new Dictionary<string, object>();
                foreach (var columnName in _columnNames)
                {
                    editData[columnName] = selectedItem.GetProperty(columnName);
                }

                var editWindow = new EditWindow("受給者データ編集", _columnNames, editData);
                if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                {
                    // 获取No值用于更新
                    var noValue = selectedItem.GetProperty("No")?.ToString();
                    if (int.TryParse(noValue, out int no))
                    {
                        // 创建更新的RecipientData对象
                        var recipientData = new RecipientData
                        {
                            No = no,
                            登録日 = editWindow.ResultData.GetValueOrDefault("登録日", "").ToString(),
                            事業者番号 = editWindow.ResultData.GetValueOrDefault("事業者番号", "").ToString(),
                            事業者郵便番号 = editWindow.ResultData.GetValueOrDefault("事業者郵便番号", "").ToString(),
                            事業者住所 = editWindow.ResultData.GetValueOrDefault("事業者住所", "").ToString(),
                            事業者名称 = editWindow.ResultData.GetValueOrDefault("事業者名称", "").ToString(),
                            代表者名 = editWindow.ResultData.GetValueOrDefault("代表者名", "").ToString(),
                            代表者役職 = editWindow.ResultData.GetValueOrDefault("代表者役職", "").ToString(),
                            サービス提供年月 = editWindow.ResultData.GetValueOrDefault("サービス提供年月", "").ToString(),
                            明細書件数 = editWindow.ResultData.GetValueOrDefault("明細書件数", "0").ToString(),
                            請求金額 = editWindow.ResultData.GetValueOrDefault("請求金額", "0").ToString(),
                            第三者評価 = editWindow.ResultData.GetValueOrDefault("第三者評価", "").ToString(),
                            受給者番号 = editWindow.ResultData.GetValueOrDefault("受給者番号", "").ToString(),
                            支給決定障害者氏名 = editWindow.ResultData.GetValueOrDefault("支給決定障害者氏名", "").ToString(),
                            支給決定に係る障害児氏名 = editWindow.ResultData.GetValueOrDefault("支給決定に係る障害児氏名", "").ToString(),
                            障害支援区分 = editWindow.ResultData.GetValueOrDefault("障害支援区分", "").ToString(),
                            事業者名称2 = editWindow.ResultData.GetValueOrDefault("事業者名称2", "").ToString(),
                            地域区分 = editWindow.ResultData.GetValueOrDefault("地域区分", "").ToString(),
                            旧身体療護施設区分 = editWindow.ResultData.GetValueOrDefault("旧身体療護施設区分", "").ToString(),
                            精神科医療連携体制加算 = editWindow.ResultData.GetValueOrDefault("精神科医療連携体制加算", "").ToString(),
                            開始年月日 = editWindow.ResultData.GetValueOrDefault("開始年月日", "").ToString(),
                            終了年月日 = editWindow.ResultData.GetValueOrDefault("終了年月日", "").ToString(),
                            利用日数全体 = editWindow.ResultData.GetValueOrDefault("利用日数全体", "0").ToString(),
                            サービスコード = editWindow.ResultData.GetValueOrDefault("サービスコード", "").ToString(),
                            サービス内容 = editWindow.ResultData.GetValueOrDefault("サービス内容", "").ToString(),
                            算定単価額 = editWindow.ResultData.GetValueOrDefault("算定単価額", "0").ToString(),
                            利用日数 = editWindow.ResultData.GetValueOrDefault("利用日数", "0").ToString(),
                            当月算定額 = editWindow.ResultData.GetValueOrDefault("当月算定額", "0").ToString(),
                            摘要 = editWindow.ResultData.GetValueOrDefault("摘要", "").ToString(),
                            status = editWindow.ResultData.GetValueOrDefault("status", "").ToString()
                        };

                        // 更新数据库
                        await _recipientRepository.UpdateRecipientAsync(recipientData);

                        // 刷新数据
                        LoadDataFromDatabase();
                        MessageBox.Show("データの更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("レコードIDを取得できません！", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ編集に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var selectedItem = button?.Tag as RecipientServiceInfo;
            if (selectedItem == null)
            {
                MessageBox.Show("削除するレコードを選択してください！", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var recipientName = selectedItem["受給者番号"]?.ToString() ?? "不明";
            var result = MessageBox.Show($"受給者「{recipientName}」のサービス情報を削除しますか？", "削除確認",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // 获取No值用于删除
                    var noValue = selectedItem["No"]?.ToString();
                    if (int.TryParse(noValue, out int no))
                    {
                        // 从数据库删除
                        await _recipientRepository.DeleteRecipientAsync(no);

                        // 刷新数据
                        LoadDataFromDatabase();
                        MessageBox.Show("データの削除が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("レコードIDを取得できません！", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"データ削除に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                InitializeData();
                MessageBox.Show("データの更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ更新に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("エクスポート機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 打印按钮点击事件
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("印刷機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
