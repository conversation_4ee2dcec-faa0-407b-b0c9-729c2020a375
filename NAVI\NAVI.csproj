﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5D0F406F-C0D6-4ABF-B2B5-816BAF2D077C}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>NAVI</RootNamespace>
    <AssemblyName>NAVI</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>favicon-.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=13.0.1.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\sinoair\c#code\SortingClientAm\packages\Newtonsoft.Json.12.0.3\lib\net40\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="System.Data.SQLite.Core" Version="1.0.118" />
    <PackageReference Include="NPOI" Version="2.6.2" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Page Include="BusinessDataControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="LoginControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MainControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="MonthlyProcessingControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Styles\AppStyles.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Controls\PaginationControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UserManagementControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Windows\EditWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Windows\ProgressWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Windows\UserEditWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="NationalDataControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ServiceCodeDataControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProviderExcelImportControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProviderDocumentImportControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="NationalCsvImportControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="DataReconciliationControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="DataEditControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="RecipientServiceInfoControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="RecipientInfoDataControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ReconciliationResultControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="FinanceCsvExportControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="SubsidyCsvExportControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="BusinessDataControl.xaml.cs">
      <DependentUpon>BusinessDataControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="LoginControl.xaml.cs">
      <DependentUpon>LoginControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="MainControl.xaml.cs">
      <DependentUpon>MainControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Models\BusinessData.cs" />
    <Compile Include="Models\NationalData.cs" />
    <Compile Include="Models\RecipientInfo.cs" />
    <Compile Include="Models\ServiceCodeData.cs" />
    <Compile Include="Models\UserData.cs" />
    <Compile Include="MonthlyProcessingControl.xaml.cs">
      <DependentUpon>MonthlyProcessingControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Services\CsvImportService.cs" />
    <Compile Include="Services\DAL\BaseRepository.cs" />
    <Compile Include="Services\DAL\KokuhoRenRepository.cs" />
    <Compile Include="Services\DAL\RecipientRepository.cs" />
    <Compile Include="Services\DAL\ServiceCodeRepository.cs" />
    <Compile Include="Services\DAL\ServiceProviderRepository.cs" />
    <Compile Include="Services\DAL\UserRepository.cs" />
    <Compile Include="Services\DatabaseManager.cs" />
    <Compile Include="Services\DatabaseService.cs" />
    <Compile Include="Services\ExcelDataService.cs" />
    <Compile Include="Services\ProviderExcelImportService.cs" />
    <Compile Include="Services\ProviderExcelTestCreator.cs" />
    <Compile Include="Controls\PaginationControl.xaml.cs">
      <DependentUpon>PaginationControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Services\ServiceCodeCsvImportService.cs" />
    <Compile Include="UserManagementControl.xaml.cs">
      <DependentUpon>UserManagementControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Utils\ClaudeOcrHelper.cs" />
    <Compile Include="Utils\DictionaryExtensions.cs" />
    <Compile Include="Utils\ReflectionExtensions.cs" />
    <Compile Include="Windows\EditWindow.xaml.cs">
      <DependentUpon>EditWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Windows\ProgressWindow.xaml.cs">
      <DependentUpon>ProgressWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Windows\UserEditWindow.xaml.cs">
      <DependentUpon>UserEditWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="NationalDataControl.xaml.cs">
      <DependentUpon>NationalDataControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="ServiceCodeDataControl.xaml.cs">
      <DependentUpon>ServiceCodeDataControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProviderExcelImportControl.xaml.cs">
      <DependentUpon>ProviderExcelImportControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProviderDocumentImportControl.xaml.cs">
      <DependentUpon>ProviderDocumentImportControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="NationalCsvImportControl.xaml.cs">
      <DependentUpon>NationalCsvImportControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="DataReconciliationControl.xaml.cs">
      <DependentUpon>DataReconciliationControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="DataEditControl.xaml.cs">
      <DependentUpon>DataEditControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="RecipientServiceInfoControl.xaml.cs">
      <DependentUpon>RecipientServiceInfoControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="RecipientInfoDataControl.xaml.cs">
      <DependentUpon>RecipientInfoDataControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="ReconciliationResultControl.xaml.cs">
      <DependentUpon>ReconciliationResultControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="FinanceCsvExportControl.xaml.cs">
      <DependentUpon>FinanceCsvExportControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="SubsidyCsvExportControl.xaml.cs">
      <DependentUpon>SubsidyCsvExportControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Models\RecipientServiceInfo.cs" />
    <Compile Include="TestExcelCreator.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="database\navi.db" />
    <None Include="database\shortstay_app_ver0.9.xlsx" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="upload.png" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\..\..\sinoair\c#code\SortingClientAm\packages\Newtonsoft.Json.6.0.4\lib\net45\Newtonsoft.Json.dll" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="demo.ico" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="favicon-.ico" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="database\database.txt" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="MoveDllsToSubfolder" AfterTargets="Build">
    <!-- 创建目标文件夹 -->
    <MakeDir Directories="$(OutputPath)dlls" Condition="!Exists('$(OutputPath)dlls')" />
    <!-- 查找所有 .dll 文件 -->
    <ItemGroup>
      <DllFiles Include="$(OutputPath)*.dll" />
    </ItemGroup>
    <!-- 将 DLL 移动到 dlls 文件夹 -->
    <Move SourceFiles="@(DllFiles)" DestinationFiles="@(DllFiles->'$(OutputPath)dlls\%(Filename)%(Extension)')" />
  </Target>
</Project>